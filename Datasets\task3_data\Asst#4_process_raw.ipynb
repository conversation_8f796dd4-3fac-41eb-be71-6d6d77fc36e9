import os
import pandas as pd
from datetime import datetime
import shutil
import tsfel
from pathlib import Path
import numpy as np

def process_activities(raw_dir, processed_dir):
    activities = os.listdir(raw_dir)

    for activity in activities:
        raw_activity_dir = os.path.join(raw_dir, activity)
        processed_activity_dir = os.path.join(processed_dir, activity)
        
        os.makedirs(processed_activity_dir, exist_ok=True)
        
        for filename in os.listdir(raw_activity_dir):
            if filename.endswith('.csv'):
                raw_filepath = os.path.join(raw_activity_dir, filename)
                
                data = pd.read_csv(raw_filepath)
                
                data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S:%f')
                data['elapsed_time'] = (data['time'] - data['time'].iloc[0]).dt.total_seconds() * 1000
                
                downsampled_data = []

                interval = 20
                start_time = 0
                
                while start_time < data['elapsed_time'].iloc[-1]:
                    end_time = start_time + interval
                    mask = (data['elapsed_time'] >= start_time) & (data['elapsed_time'] < end_time)
                    group = data[mask]

                    if not group.empty:
                        avg_gFx = group['gFx'].mean()
                        avg_gFy = group['gFy'].mean()
                        avg_gFz = group['gFz'].mean()

                        downsampled_data.append([avg_gFx, avg_gFy, avg_gFz])
                    
                    start_time += interval

                downsampled_df = pd.DataFrame(downsampled_data, columns=['accx', 'accy', 'accz'])
                downsampled_df = downsampled_df.round(7)
                
                processed_filepath = os.path.join(processed_activity_dir, filename)
                downsampled_df.to_csv(processed_filepath, index=False)
                
                print(f"Processed and saved: {processed_filepath}")

process_activities('unprocessed', 'processed')


base_dir = 'processed'
output_dir = 'processed_trimmed'

os.makedirs(output_dir, exist_ok=True)

for activity in os.listdir(base_dir):
    activity_dir = os.path.join(base_dir, activity)
    
    if os.path.isdir(activity_dir):
        output_activity_dir = os.path.join(output_dir, activity)
        os.makedirs(output_activity_dir, exist_ok=True)
        
        for filename in os.listdir(activity_dir):
            if filename.endswith('.csv'):
                input_filepath = os.path.join(activity_dir, filename)
                output_filepath = os.path.join(output_activity_dir, filename)
                
                data = pd.read_csv(input_filepath)
                
                data_trimmed = data.iloc[175:]
                
                data_trimmed = data_trimmed.iloc[:500]
                
                data_trimmed.to_csv(output_filepath, index=False)
                
                remaining_rows = len(data) - 675
                if remaining_rows < 25:
                    print(f"Warning: {filename} in {activity} has only {remaining_rows} rows left after processing.")
                
                print(f"Processed and saved: {output_filepath}")


base_dir = 'raw_dataset'

train_dir = os.path.join(base_dir, 'Train')
test_dir = os.path.join(base_dir, 'Test')

activity_dirs = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_DOWNSTAIRS', 'WALKING_UPSTAIRS']

files_to_move = ['Subject_9.csv', 'Subject_10.csv', 'Subject_11.csv', 'Subject_12.csv']

for activity in activity_dirs:
    activity_train_dir = os.path.join(train_dir, activity)
    activity_test_dir = os.path.join(test_dir, activity)
    
    os.makedirs(activity_test_dir, exist_ok=True)

    # Move Subject_8.csv and Subject_12.csv from Train to Test
    for file_name in files_to_move:
        train_file_path = os.path.join(activity_train_dir, file_name)
        test_file_path = os.path.join(activity_test_dir, file_name)
        
        if os.path.exists(train_file_path):
            shutil.move(train_file_path, test_file_path)
            print(f"Moved {file_name} from {activity_train_dir} to {activity_test_dir}")

for activity in activity_dirs:
    activity_test_dir = os.path.join(test_dir, activity)
    
    test_files = os.listdir(activity_test_dir)
    
    for file_name in test_files:
        if file_name not in files_to_move:
            file_path = os.path.join(activity_test_dir, file_name)
            os.remove(file_path)
            print(f"Removed {file_name} from {activity_test_dir}")


base_dir = 'raw_dataset/Train'
output_base_dir = 'TSFEL_dataset/Train'

activities = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_UPSTAIRS', 'WALKING_DOWNSTAIRS']

for activity in activities:
    activity_dir = os.path.join(base_dir, activity)
    output_activity_dir = os.path.join(output_base_dir, activity)
    Path(output_activity_dir).mkdir(parents=True, exist_ok=True)
    subject_files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]
    for file in subject_files:
        file_path = os.path.join(activity_dir, file)
        df = pd.read_csv(file_path).iloc[:, :]
        cfg = tsfel.get_features_by_domain() 
        # print(cfg)
        for domain in cfg:
            for feature in cfg[domain]:
                cfg[domain][feature]['use'] = 'yes' # use all features, even ones disabled by default

        features = tsfel.time_series_features_extractor(cfg, df, fs=50) # sampling rate 50 Hz
        subject_id = file.split('.')[0]
        output_file = os.path.join(output_activity_dir, f'{subject_id}.csv')
        features.to_csv(output_file, index=False)




selected_feature_indices = np.array([
    0, 1, 3, 6, 7, 260, 298, 300, 302, 303, 304, 306, 308, 311,
    315, 319, 325, 335, 336, 339, 340, 341, 342, 343, 344, 345,
    346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 357, 358,
    359, 360, 361, 362, 363, 364, 365, 374, 679, 683, 687, 689,
    690, 715, 726, 727, 728, 736, 737, 738, 744, 1058
])

print(selected_feature_indices)

original_dir = 'TSFEL_dataset'
filtered_dir = 'TSFEL_filtereddataset'
shutil.copytree(original_dir, filtered_dir)

feature_names = None

for root, dirs, files in os.walk(filtered_dir):
    for file in files:
        if file.endswith('.csv'):
            file_path = os.path.join(root, file)
            
            df = pd.read_csv(file_path)
            
            if feature_names is None:
                feature_names = df.columns.tolist()
            
            df_filtered = df.iloc[:, selected_feature_indices]
            
            df_filtered.to_csv(file_path, index=False)

if feature_names is not None:
    print("List of features in the dataset:")
    for i, feature in enumerate(feature_names):
        print(f"{i+1}: {feature}")

print(f"Filtered features saved in the directory: {filtered_dir}")
