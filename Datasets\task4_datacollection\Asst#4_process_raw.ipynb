{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 2. Pre-processing and Feature Extraction (On Raw collected data)\n", "\n", "In this notebook, we pre-process the raw data collected from the sensors and extract features from it.\n", "\n", "We used the `Physics Toolbox Suite` app to collect the data. We use the gForce meter from the app. However there were some difficulties which we faced when preprocessing the data. The data that was collected was sampled at the highest frequency possible from our mobile. Hence the samples were also not evenly spaced. The sampling frequency kept changing between 190 Hz to 210 Hz. This made it difficult to use the data as it is. So we took a sliding window of 20 miliseconds and calculated the mean of the samples in that window. This way we were able to get evenly spaced samples at a frequency of 50 Hz.\n", "\n", "For spliting into test and train datasets, we used the leave one subject out stratagy to split the data into testing and training. After processing all the data we save them in the same format as the UCI HAR and TSFEL datasets directory structure.\n", "\n", "Here is the explainiation of all the directories that are there in this folder:\n", "\n", "- `./unprocessed`: This dataset contains the raw g-force data captured directly from the device, sampled at the highest available frequency, which varies between 190 and 210 Hz.\n", "\n", "- `./processed`: The dataset has been downsampled to 50 Hz by segmenting the data into 20 ms intervals and computing the average acceleration within each segment.\n", "\n", "- `./processed_trimmed`: In this dataset, the first 4.5 seconds (225 rows) of the time series data have been removed, as well as the final 0.5 seconds (25 rows), to mitigate any noise present at the start and end of the recordings.\n", "\n", "- `./raw_dataset`: This dataset is divided into training and testing subsets. The testing subset specifically contains activity data for <PERSON><PERSON>, while the training subset includes data from all other subjects.\n", "\n", "- `./TSFEL_features`: This dataset includes 1173 features extracted using TSFEL (Time Series Feature Extraction Library) from the raw_dataset. It does not contain any train-test splits.\n", "\n", "- `./TSFEL_dataset`: This dataset also includes 1173 features extracted using TSFEL from the raw_dataset, but it is organized with predefined train-test splits.\n", "\n", "We have followed the following steps to preprocess the data:\n", "\n", "- Process the raw data to get evenly spaced samples at 50 Hz.\n", "- Trim the data to remove the first 4.5 seconds and the last 0.5 seconds (get a 10s window)\n", "- Split the data into training and testing datasets using the leave one subject out strategy.\n", "- Extract features from the data using the Time Series Feature Extraction Library (TSFEL).\n", "- Filter out the TSFEL features as ber ANOVA F-test."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "from datetime import datetime\n", "import shutil\n", "import tsfel\n", "from pathlib import Path\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing the data to get evenly spaced samples at a frequency of 50 Hz"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed and saved: processed\\LAYING\\Subject_1.csv\n", "Processed and saved: processed\\LAYING\\Subject_10.csv\n", "Processed and saved: processed\\LAYING\\Subject_11.csv\n", "Processed and saved: processed\\LAYING\\Subject_12.csv\n", "Processed and saved: processed\\LAYING\\Subject_2.csv\n", "Processed and saved: processed\\LAYING\\Subject_3.csv\n", "Processed and saved: processed\\LAYING\\Subject_4.csv\n", "Processed and saved: processed\\LAYING\\Subject_5.csv\n", "Processed and saved: processed\\LAYING\\Subject_6.csv\n", "Processed and saved: processed\\LAYING\\Subject_7.csv\n", "Processed and saved: processed\\LAYING\\Subject_8.csv\n", "Processed and saved: processed\\LAYING\\Subject_9.csv\n", "Processed and saved: processed\\SITTING\\Subject_1.csv\n", "Processed and saved: processed\\SITTING\\Subject_10.csv\n", "Processed and saved: processed\\SITTING\\Subject_11.csv\n", "Processed and saved: processed\\SITTING\\Subject_12.csv\n", "Processed and saved: processed\\SITTING\\Subject_2.csv\n", "Processed and saved: processed\\SITTING\\Subject_3.csv\n", "Processed and saved: processed\\SITTING\\Subject_4.csv\n", "Processed and saved: processed\\SITTING\\Subject_5.csv\n", "Processed and saved: processed\\SITTING\\Subject_6.csv\n", "Processed and saved: processed\\SITTING\\Subject_7.csv\n", "Processed and saved: processed\\SITTING\\Subject_8.csv\n", "Processed and saved: processed\\SITTING\\Subject_9.csv\n", "Processed and saved: processed\\STANDING\\Subject_1.csv\n", "Processed and saved: processed\\STANDING\\Subject_10.csv\n", "Processed and saved: processed\\STANDING\\Subject_11.csv\n", "Processed and saved: processed\\STANDING\\Subject_12.csv\n", "Processed and saved: processed\\STANDING\\Subject_10.csv\n", "Processed and saved: processed\\STANDING\\Subject_11.csv\n", "Processed and saved: processed\\STANDING\\Subject_12.csv\n", "Processed and saved: processed\\STANDING\\Subject_2.csv\n", "Processed and saved: processed\\STANDING\\Subject_3.csv\n", "Processed and saved: processed\\STANDING\\Subject_4.csv\n", "Processed and saved: processed\\STANDING\\Subject_5.csv\n", "Processed and saved: processed\\STANDING\\Subject_6.csv\n", "Processed and saved: processed\\STANDING\\Subject_7.csv\n", "Processed and saved: processed\\STANDING\\Subject_8.csv\n", "Processed and saved: processed\\STANDING\\Subject_9.csv\n", "Processed and saved: processed\\STANDING\\Subject_9.csv\n", "Processed and saved: processed\\WALKING\\Subject_1.csv\n", "Processed and saved: processed\\WALKING\\Subject_10.csv\n", "Processed and saved: processed\\WALKING\\Subject_11.csv\n", "Processed and saved: processed\\WALKING\\Subject_12.csv\n", "Processed and saved: processed\\WALKING\\Subject_2.csv\n", "Processed and saved: processed\\WALKING\\Subject_3.csv\n", "Processed and saved: processed\\WALKING\\Subject_4.csv\n", "Processed and saved: processed\\WALKING\\Subject_5.csv\n", "Processed and saved: processed\\WALKING\\Subject_6.csv\n", "Processed and saved: processed\\WALKING\\Subject_7.csv\n", "Processed and saved: processed\\WALKING\\Subject_8.csv\n", "Processed and saved: processed\\WALKING\\Subject_9.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_1.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_10.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_11.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_12.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_2.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_3.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_4.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_5.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_6.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_7.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_8.csv\n", "Processed and saved: processed\\WALKING_DOWNSTAIRS\\Subject_9.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_1.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_10.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_11.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_12.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_2.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_3.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_4.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_5.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_6.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_7.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_8.csv\n", "Processed and saved: processed\\WALKING_UPSTAIRS\\Subject_9.csv\n"]}], "source": ["def process_activities(raw_dir, processed_dir):\n", "    activities = os.listdir(raw_dir)\n", "\n", "    for activity in activities:\n", "        raw_activity_dir = os.path.join(raw_dir, activity)\n", "        processed_activity_dir = os.path.join(processed_dir, activity)\n", "        \n", "        os.makedirs(processed_activity_dir, exist_ok=True)\n", "        \n", "        for filename in os.listdir(raw_activity_dir):\n", "            if filename.endswith('.csv'):\n", "                raw_filepath = os.path.join(raw_activity_dir, filename)\n", "                \n", "                data = pd.read_csv(raw_filepath)\n", "                \n", "                data['time'] = pd.to_datetime(data['time'], format='%H:%M:%S:%f')\n", "                data['elapsed_time'] = (data['time'] - data['time'].iloc[0]).dt.total_seconds() * 1000\n", "                \n", "                downsampled_data = []\n", "\n", "                interval = 20\n", "                start_time = 0\n", "                \n", "                while start_time < data['elapsed_time'].iloc[-1]:\n", "                    end_time = start_time + interval\n", "                    mask = (data['elapsed_time'] >= start_time) & (data['elapsed_time'] < end_time)\n", "                    group = data[mask]\n", "\n", "                    if not group.empty:\n", "                        avg_gFx = group['gFx'].mean()\n", "                        avg_gFy = group['gFy'].mean()\n", "                        avg_gFz = group['gFz'].mean()\n", "\n", "                        downsampled_data.append([avg_gFx, avg_gFy, avg_gFz])\n", "                    \n", "                    start_time += interval\n", "\n", "                downsampled_df = pd.DataFrame(downsampled_data, columns=['accx', 'accy', 'accz'])\n", "                downsampled_df = downsampled_df.round(7)\n", "                \n", "                processed_filepath = os.path.join(processed_activity_dir, filename)\n", "                downsampled_df.to_csv(processed_filepath, index=False)\n", "                \n", "                print(f\"Processed and saved: {processed_filepath}\")\n", "\n", "process_activities('unprocessed', 'processed')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Triming the data into required duration:\n", "- 10 seconds of data will be taken from the total data. THis will be done by removing the first 4.5 seconds and the last 0.5 seconds. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed and saved: processed_trimmed\\LAYING\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\LAYING\\Subject_9.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\SITTING\\Subject_9.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\STANDING\\Subject_9.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\WALKING\\Subject_9.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\WALKING_DOWNSTAIRS\\Subject_9.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_1.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_10.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_11.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_12.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_2.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_3.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_4.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_5.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_6.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_7.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_8.csv\n", "Processed and saved: processed_trimmed\\WALKING_UPSTAIRS\\Subject_9.csv\n"]}], "source": ["base_dir = 'processed'\n", "output_dir = 'processed_trimmed'\n", "\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "for activity in os.listdir(base_dir):\n", "    activity_dir = os.path.join(base_dir, activity)\n", "    \n", "    if os.path.isdir(activity_dir):\n", "        output_activity_dir = os.path.join(output_dir, activity)\n", "        os.makedirs(output_activity_dir, exist_ok=True)\n", "        \n", "        for filename in os.listdir(activity_dir):\n", "            if filename.endswith('.csv'):\n", "                input_filepath = os.path.join(activity_dir, filename)\n", "                output_filepath = os.path.join(output_activity_dir, filename)\n", "                \n", "                data = pd.read_csv(input_filepath)\n", "                \n", "                data_trimmed = data.iloc[175:]\n", "                \n", "                data_trimmed = data_trimmed.iloc[:500]\n", "                \n", "                data_trimmed.to_csv(output_filepath, index=False)\n", "                \n", "                remaining_rows = len(data) - 675\n", "                if remaining_rows < 25:\n", "                    print(f\"Warning: {filename} in {activity} has only {remaining_rows} rows left after processing.\")\n", "                \n", "                print(f\"Processed and saved: {output_filepath}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Leave one subject out stratagy to split the data into testing and training"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Moved Subject_9.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_10.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_11.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_12.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_9.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_10.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_11.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_12.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_9.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_10.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_11.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_12.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_9.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_10.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_11.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_9.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_10.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_11.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_9.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Moved Subject_10.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Moved Subject_11.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_1.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_2.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_3.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_4.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_5.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_6.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_7.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_8.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_1.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_2.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_3.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_4.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_5.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_6.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_7.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_8.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_1.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_2.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_3.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_4.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_5.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_6.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_7.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_8.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_4.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_8.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_4.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_8.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_4.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_8.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n"]}], "source": ["base_dir = 'raw_dataset'\n", "\n", "train_dir = os.path.join(base_dir, 'Train')\n", "test_dir = os.path.join(base_dir, 'Test')\n", "\n", "activity_dirs = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_DOWNSTAIRS', 'WALKING_UPSTAIRS']\n", "\n", "files_to_move = ['Subject_9.csv', 'Subject_10.csv', 'Subject_11.csv', 'Subject_12.csv']\n", "\n", "for activity in activity_dirs:\n", "    activity_train_dir = os.path.join(train_dir, activity)\n", "    activity_test_dir = os.path.join(test_dir, activity)\n", "    \n", "    os.makedirs(activity_test_dir, exist_ok=True)\n", "\n", "    # Move Subject_8.csv and Subject_12.csv from Train to Test\n", "    for file_name in files_to_move:\n", "        train_file_path = os.path.join(activity_train_dir, file_name)\n", "        test_file_path = os.path.join(activity_test_dir, file_name)\n", "        \n", "        if os.path.exists(train_file_path):\n", "            shutil.move(train_file_path, test_file_path)\n", "            print(f\"Moved {file_name} from {activity_train_dir} to {activity_test_dir}\")\n", "\n", "for activity in activity_dirs:\n", "    activity_test_dir = os.path.join(test_dir, activity)\n", "    \n", "    test_files = os.listdir(activity_test_dir)\n", "    \n", "    for file_name in test_files:\n", "        if file_name not in files_to_move:\n", "            file_path = os.path.join(activity_test_dir, file_name)\n", "            os.remove(file_path)\n", "            print(f\"Removed {file_name} from {activity_test_dir}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating TSFEL Features on our dataset:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 5% Complete\n", "              <p/>\n", "              <progress\n", "                  value='4'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  4\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n", "*** Feature extraction started ***\n"]}, {"data": {"text/html": ["\n", "              <p>\n", "                  Progress: 100% Complete\n", "              <p/>\n", "              <progress\n", "                  value='67'\n", "                  max='67',\n", "                  style='width: 25%',\n", "              >\n", "                  67\n", "              </progress>\n", "\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "*** Feature extraction finished ***\n"]}], "source": ["base_dir = 'raw_dataset/Train'\n", "output_base_dir = 'TSFEL_dataset/Train'\n", "\n", "activities = ['LAYING', 'SITTING', 'STANDING', 'WALKING', 'WALKING_UPSTAIRS', 'WALKING_DOWNSTAIRS']\n", "\n", "for activity in activities:\n", "    activity_dir = os.path.join(base_dir, activity)\n", "    output_activity_dir = os.path.join(output_base_dir, activity)\n", "    Path(output_activity_dir).mkdir(parents=True, exist_ok=True)\n", "    subject_files = [f for f in os.listdir(activity_dir) if f.endswith('.csv')]\n", "    for file in subject_files:\n", "        file_path = os.path.join(activity_dir, file)\n", "        df = pd.read_csv(file_path).iloc[:, :]\n", "        cfg = tsfel.get_features_by_domain() \n", "        # print(cfg)\n", "        for domain in cfg:\n", "            for feature in cfg[domain]:\n", "                cfg[domain][feature]['use'] = 'yes' # use all features, even ones disabled by default\n", "\n", "        features = tsfel.time_series_features_extractor(cfg, df, fs=50) # sampling rate 50 Hz\n", "        subject_id = file.split('.')[0]\n", "        output_file = os.path.join(output_activity_dir, f'{subject_id}.csv')\n", "        features.to_csv(output_file, index=False)\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Moved Subject_4.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_8.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_12.csv from raw_dataset\\Train\\LAYING to raw_dataset\\Test\\LAYING\n", "Moved Subject_4.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_8.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_12.csv from raw_dataset\\Train\\SITTING to raw_dataset\\Test\\SITTING\n", "Moved Subject_4.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_8.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_12.csv from raw_dataset\\Train\\STANDING to raw_dataset\\Test\\STANDING\n", "Moved Subject_4.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_8.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING to raw_dataset\\Test\\WALKING\n", "Moved Subject_4.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_8.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING_DOWNSTAIRS to raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Moved Subject_4.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Moved Subject_8.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Moved Subject_12.csv from raw_dataset\\Train\\WALKING_UPSTAIRS to raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_1.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_10.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_11.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_2.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_3.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_5.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_6.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_7.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_9.csv from raw_dataset\\Test\\LAYING\n", "Removed Subject_1.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_10.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_11.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_2.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_3.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_5.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_6.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_7.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_9.csv from raw_dataset\\Test\\SITTING\n", "Removed Subject_1.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_10.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_11.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_2.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_3.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_5.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_6.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_7.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_9.csv from raw_dataset\\Test\\STANDING\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_10.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_11.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_9.csv from raw_dataset\\Test\\WALKING\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_10.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_11.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_9.csv from raw_dataset\\Test\\WALKING_DOWNSTAIRS\n", "Removed Subject_1.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_10.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_11.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_2.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_3.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_5.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_6.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_7.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n", "Removed Subject_9.csv from raw_dataset\\Test\\WALKING_UPSTAIRS\n"]}], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filtering TSFEL Features as per best 60 ANOVA F-Value"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[   0    1    3    6    7  260  298  300  302  303  304  306  308  311\n", "  315  319  325  335  336  339  340  341  342  343  344  345  346  347\n", "  348  349  350  351  352  353  354  355  357  358  359  360  361  362\n", "  363  364  365  374  679  683  687  689  690  715  726  727  728  736\n", "  737  738  744 1058]\n", "List of features in the dataset:\n", "1: accx_Absolute energy\n", "2: accx_Area under the curve\n", "3: accx_Autocorrelation\n", "4: accx_Average power\n", "5: accx_Centroid\n", "6: accx_Detrended fluctuation analysis\n", "7: accx_ECDF Percentile Count_0\n", "8: accx_ECDF Percentile Count_1\n", "9: accx_ECDF Percentile_0\n", "10: accx_ECDF Percentile_1\n", "11: accx_ECDF_0\n", "12: accx_ECDF_1\n", "13: accx_ECDF_2\n", "14: accx_ECDF_3\n", "15: accx_ECDF_4\n", "16: accx_ECDF_5\n", "17: accx_ECDF_6\n", "18: accx_ECDF_7\n", "19: accx_ECDF_8\n", "20: accx_ECDF_9\n", "21: accx_Entropy\n", "22: accx_FFT mean coefficient_0\n", "23: accx_FFT mean coefficient_1\n", "24: accx_FFT mean coefficient_10\n", "25: accx_FFT mean coefficient_100\n", "26: accx_FFT mean coefficient_101\n", "27: accx_FFT mean coefficient_102\n", "28: accx_FFT mean coefficient_103\n", "29: accx_FFT mean coefficient_104\n", "30: accx_FFT mean coefficient_105\n", "31: accx_FFT mean coefficient_106\n", "32: accx_FFT mean coefficient_107\n", "33: accx_FFT mean coefficient_108\n", "34: accx_FFT mean coefficient_109\n", "35: accx_FFT mean coefficient_11\n", "36: accx_FFT mean coefficient_110\n", "37: accx_FFT mean coefficient_111\n", "38: accx_FFT mean coefficient_112\n", "39: accx_FFT mean coefficient_113\n", "40: accx_FFT mean coefficient_114\n", "41: accx_FFT mean coefficient_115\n", "42: accx_FFT mean coefficient_116\n", "43: accx_FFT mean coefficient_117\n", "44: accx_FFT mean coefficient_118\n", "45: accx_FFT mean coefficient_119\n", "46: accx_FFT mean coefficient_12\n", "47: accx_FFT mean coefficient_120\n", "48: accx_FFT mean coefficient_121\n", "49: accx_FFT mean coefficient_122\n", "50: accx_FFT mean coefficient_123\n", "51: accx_FFT mean coefficient_124\n", "52: accx_FFT mean coefficient_125\n", "53: accx_FFT mean coefficient_126\n", "54: accx_FFT mean coefficient_127\n", "55: accx_FFT mean coefficient_128\n", "56: accx_FFT mean coefficient_129\n", "57: accx_FFT mean coefficient_13\n", "58: accx_FFT mean coefficient_130\n", "59: accx_FFT mean coefficient_131\n", "60: accx_FFT mean coefficient_132\n", "61: accx_FFT mean coefficient_133\n", "62: accx_FFT mean coefficient_134\n", "63: accx_FFT mean coefficient_135\n", "64: accx_FFT mean coefficient_136\n", "65: accx_FFT mean coefficient_137\n", "66: accx_FFT mean coefficient_138\n", "67: accx_FFT mean coefficient_139\n", "68: accx_FFT mean coefficient_14\n", "69: accx_FFT mean coefficient_140\n", "70: accx_FFT mean coefficient_141\n", "71: accx_FFT mean coefficient_142\n", "72: accx_FFT mean coefficient_143\n", "73: accx_FFT mean coefficient_144\n", "74: accx_FFT mean coefficient_145\n", "75: accx_FFT mean coefficient_146\n", "76: accx_FFT mean coefficient_147\n", "77: accx_FFT mean coefficient_148\n", "78: accx_FFT mean coefficient_149\n", "79: accx_FFT mean coefficient_15\n", "80: accx_FFT mean coefficient_150\n", "81: accx_FFT mean coefficient_151\n", "82: accx_FFT mean coefficient_152\n", "83: accx_FFT mean coefficient_153\n", "84: accx_FFT mean coefficient_154\n", "85: accx_FFT mean coefficient_155\n", "86: accx_FFT mean coefficient_156\n", "87: accx_FFT mean coefficient_157\n", "88: accx_FFT mean coefficient_158\n", "89: accx_FFT mean coefficient_159\n", "90: accx_FFT mean coefficient_16\n", "91: accx_FFT mean coefficient_160\n", "92: accx_FFT mean coefficient_161\n", "93: accx_FFT mean coefficient_162\n", "94: accx_FFT mean coefficient_163\n", "95: accx_FFT mean coefficient_164\n", "96: accx_FFT mean coefficient_165\n", "97: accx_FFT mean coefficient_166\n", "98: accx_FFT mean coefficient_167\n", "99: accx_FFT mean coefficient_168\n", "100: accx_FFT mean coefficient_169\n", "101: accx_FFT mean coefficient_17\n", "102: accx_FFT mean coefficient_170\n", "103: accx_FFT mean coefficient_171\n", "104: accx_FFT mean coefficient_172\n", "105: accx_FFT mean coefficient_173\n", "106: accx_FFT mean coefficient_174\n", "107: accx_FFT mean coefficient_175\n", "108: accx_FFT mean coefficient_176\n", "109: accx_FFT mean coefficient_177\n", "110: accx_FFT mean coefficient_178\n", "111: accx_FFT mean coefficient_179\n", "112: accx_FFT mean coefficient_18\n", "113: accx_FFT mean coefficient_180\n", "114: accx_FFT mean coefficient_181\n", "115: accx_FFT mean coefficient_182\n", "116: accx_FFT mean coefficient_183\n", "117: accx_FFT mean coefficient_184\n", "118: accx_FFT mean coefficient_185\n", "119: accx_FFT mean coefficient_186\n", "120: accx_FFT mean coefficient_187\n", "121: accx_FFT mean coefficient_188\n", "122: accx_FFT mean coefficient_189\n", "123: accx_FFT mean coefficient_19\n", "124: accx_FFT mean coefficient_190\n", "125: accx_FFT mean coefficient_191\n", "126: accx_FFT mean coefficient_192\n", "127: accx_FFT mean coefficient_193\n", "128: accx_FFT mean coefficient_194\n", "129: accx_FFT mean coefficient_195\n", "130: accx_FFT mean coefficient_196\n", "131: accx_FFT mean coefficient_197\n", "132: accx_FFT mean coefficient_198\n", "133: accx_FFT mean coefficient_199\n", "134: accx_FFT mean coefficient_2\n", "135: accx_FFT mean coefficient_20\n", "136: accx_FFT mean coefficient_200\n", "137: accx_FFT mean coefficient_201\n", "138: accx_FFT mean coefficient_202\n", "139: accx_FFT mean coefficient_203\n", "140: accx_FFT mean coefficient_204\n", "141: accx_FFT mean coefficient_205\n", "142: accx_FFT mean coefficient_206\n", "143: accx_FFT mean coefficient_207\n", "144: accx_FFT mean coefficient_208\n", "145: accx_FFT mean coefficient_209\n", "146: accx_FFT mean coefficient_21\n", "147: accx_FFT mean coefficient_210\n", "148: accx_FFT mean coefficient_211\n", "149: accx_FFT mean coefficient_212\n", "150: accx_FFT mean coefficient_213\n", "151: accx_FFT mean coefficient_214\n", "152: accx_FFT mean coefficient_215\n", "153: accx_FFT mean coefficient_216\n", "154: accx_FFT mean coefficient_217\n", "155: accx_FFT mean coefficient_218\n", "156: accx_FFT mean coefficient_219\n", "157: accx_FFT mean coefficient_22\n", "158: accx_FFT mean coefficient_220\n", "159: accx_FFT mean coefficient_221\n", "160: accx_FFT mean coefficient_222\n", "161: accx_FFT mean coefficient_223\n", "162: accx_FFT mean coefficient_224\n", "163: accx_FFT mean coefficient_225\n", "164: accx_FFT mean coefficient_226\n", "165: accx_FFT mean coefficient_227\n", "166: accx_FFT mean coefficient_228\n", "167: accx_FFT mean coefficient_229\n", "168: accx_FFT mean coefficient_23\n", "169: accx_FFT mean coefficient_230\n", "170: accx_FFT mean coefficient_231\n", "171: accx_FFT mean coefficient_232\n", "172: accx_FFT mean coefficient_233\n", "173: accx_FFT mean coefficient_234\n", "174: accx_FFT mean coefficient_235\n", "175: accx_FFT mean coefficient_236\n", "176: accx_FFT mean coefficient_237\n", "177: accx_FFT mean coefficient_238\n", "178: accx_FFT mean coefficient_239\n", "179: accx_FFT mean coefficient_24\n", "180: accx_FFT mean coefficient_240\n", "181: accx_FFT mean coefficient_241\n", "182: accx_FFT mean coefficient_242\n", "183: accx_FFT mean coefficient_243\n", "184: accx_FFT mean coefficient_244\n", "185: accx_FFT mean coefficient_245\n", "186: accx_FFT mean coefficient_246\n", "187: accx_FFT mean coefficient_247\n", "188: accx_FFT mean coefficient_248\n", "189: accx_FFT mean coefficient_249\n", "190: accx_FFT mean coefficient_25\n", "191: accx_FFT mean coefficient_250\n", "192: accx_FFT mean coefficient_26\n", "193: accx_FFT mean coefficient_27\n", "194: accx_FFT mean coefficient_28\n", "195: accx_FFT mean coefficient_29\n", "196: accx_FFT mean coefficient_3\n", "197: accx_FFT mean coefficient_30\n", "198: accx_FFT mean coefficient_31\n", "199: accx_FFT mean coefficient_32\n", "200: accx_FFT mean coefficient_33\n", "201: accx_FFT mean coefficient_34\n", "202: accx_FFT mean coefficient_35\n", "203: accx_FFT mean coefficient_36\n", "204: accx_FFT mean coefficient_37\n", "205: accx_FFT mean coefficient_38\n", "206: accx_FFT mean coefficient_39\n", "207: accx_FFT mean coefficient_4\n", "208: accx_FFT mean coefficient_40\n", "209: accx_FFT mean coefficient_41\n", "210: accx_FFT mean coefficient_42\n", "211: accx_FFT mean coefficient_43\n", "212: accx_FFT mean coefficient_44\n", "213: accx_FFT mean coefficient_45\n", "214: accx_FFT mean coefficient_46\n", "215: accx_FFT mean coefficient_47\n", "216: accx_FFT mean coefficient_48\n", "217: accx_FFT mean coefficient_49\n", "218: accx_FFT mean coefficient_5\n", "219: accx_FFT mean coefficient_50\n", "220: accx_FFT mean coefficient_51\n", "221: accx_FFT mean coefficient_52\n", "222: accx_FFT mean coefficient_53\n", "223: accx_FFT mean coefficient_54\n", "224: accx_FFT mean coefficient_55\n", "225: accx_FFT mean coefficient_56\n", "226: accx_FFT mean coefficient_57\n", "227: accx_FFT mean coefficient_58\n", "228: accx_FFT mean coefficient_59\n", "229: accx_FFT mean coefficient_6\n", "230: accx_FFT mean coefficient_60\n", "231: accx_FFT mean coefficient_61\n", "232: accx_FFT mean coefficient_62\n", "233: accx_FFT mean coefficient_63\n", "234: accx_FFT mean coefficient_64\n", "235: accx_FFT mean coefficient_65\n", "236: accx_FFT mean coefficient_66\n", "237: accx_FFT mean coefficient_67\n", "238: accx_FFT mean coefficient_68\n", "239: accx_FFT mean coefficient_69\n", "240: accx_FFT mean coefficient_7\n", "241: accx_FFT mean coefficient_70\n", "242: accx_FFT mean coefficient_71\n", "243: accx_FFT mean coefficient_72\n", "244: accx_FFT mean coefficient_73\n", "245: accx_FFT mean coefficient_74\n", "246: accx_FFT mean coefficient_75\n", "247: accx_FFT mean coefficient_76\n", "248: accx_FFT mean coefficient_77\n", "249: accx_FFT mean coefficient_78\n", "250: accx_FFT mean coefficient_79\n", "251: accx_FFT mean coefficient_8\n", "252: accx_FFT mean coefficient_80\n", "253: accx_FFT mean coefficient_81\n", "254: accx_FFT mean coefficient_82\n", "255: accx_FFT mean coefficient_83\n", "256: accx_FFT mean coefficient_84\n", "257: accx_FFT mean coefficient_85\n", "258: accx_FFT mean coefficient_86\n", "259: accx_FFT mean coefficient_87\n", "260: accx_FFT mean coefficient_88\n", "261: accx_FFT mean coefficient_89\n", "262: accx_FFT mean coefficient_9\n", "263: accx_FFT mean coefficient_90\n", "264: accx_FFT mean coefficient_91\n", "265: accx_FFT mean coefficient_92\n", "266: accx_FFT mean coefficient_93\n", "267: accx_FFT mean coefficient_94\n", "268: accx_FFT mean coefficient_95\n", "269: accx_FFT mean coefficient_96\n", "270: accx_FFT mean coefficient_97\n", "271: accx_FFT mean coefficient_98\n", "272: accx_FFT mean coefficient_99\n", "273: accx_Fundamental frequency\n", "274: accx_<PERSON>guchi fractal dimension\n", "275: accx_Histogram_0\n", "276: accx_Histogram_1\n", "277: accx_Histogram_2\n", "278: accx_Histogram_3\n", "279: accx_Histogram_4\n", "280: accx_Histogram_5\n", "281: accx_Histogram_6\n", "282: accx_Histogram_7\n", "283: accx_Histogram_8\n", "284: accx_Histogram_9\n", "285: accx_Human range energy\n", "286: accx_Hurst exponent\n", "287: accx_Interquartile range\n", "288: accx_<PERSON><PERSON>\n", "289: accx_LPCC_0\n", "290: accx_LPCC_1\n", "291: accx_LPCC_10\n", "292: accx_LPCC_11\n", "293: accx_LPCC_2\n", "294: accx_LPCC_3\n", "295: accx_LPCC_4\n", "296: accx_LPCC_5\n", "297: accx_LPCC_6\n", "298: accx_LPCC_7\n", "299: accx_LPCC_8\n", "300: accx_LPCC_9\n", "301: accx_Lempel-Ziv complexity\n", "302: accx_MFCC_0\n", "303: accx_MFCC_1\n", "304: accx_MFCC_10\n", "305: accx_MFCC_11\n", "306: accx_MFCC_2\n", "307: accx_MFCC_3\n", "308: accx_MFCC_4\n", "309: accx_MFCC_5\n", "310: accx_MFCC_6\n", "311: accx_MFCC_7\n", "312: accx_MFCC_8\n", "313: accx_MFCC_9\n", "314: accx_Max\n", "315: accx_Max power spectrum\n", "316: accx_Maximum fractal length\n", "317: accx_Maximum frequency\n", "318: accx_Mean\n", "319: accx_Mean absolute deviation\n", "320: accx_Mean absolute diff\n", "321: accx_Mean diff\n", "322: accx_Median\n", "323: accx_Median absolute deviation\n", "324: accx_Median absolute diff\n", "325: accx_Median diff\n", "326: accx_Median frequency\n", "327: accx_Min\n", "328: accx_Multiscale entropy\n", "329: accx_Negative turning points\n", "330: accx_Neighbourhood peaks\n", "331: accx_Peak to peak distance\n", "332: accx_Petrosian fractal dimension\n", "333: accx_Positive turning points\n", "334: accx_Power bandwidth\n", "335: accx_Root mean square\n", "336: accx_Signal distance\n", "337: accx_Skewness\n", "338: accx_Slope\n", "339: accx_Spectral centroid\n", "340: accx_Spectral decrease\n", "341: accx_Spectral distance\n", "342: accx_Spectral entropy\n", "343: accx_Spectral kurtosis\n", "344: accx_Spectral positive turning points\n", "345: accx_Spectral roll-off\n", "346: accx_Spectral roll-on\n", "347: accx_Spectral skewness\n", "348: accx_Spectral slope\n", "349: accx_Spectral spread\n", "350: accx_Spectral variation\n", "351: accx_Standard deviation\n", "352: accx_Sum absolute diff\n", "353: accx_<PERSON><PERSON>ce\n", "354: accx_Wavelet absolute mean_0\n", "355: accx_Wavelet absolute mean_1\n", "356: accx_Wavelet absolute mean_2\n", "357: accx_Wavelet absolute mean_3\n", "358: accx_Wavelet absolute mean_4\n", "359: accx_Wavelet absolute mean_5\n", "360: accx_Wavelet absolute mean_6\n", "361: accx_Wavelet absolute mean_7\n", "362: accx_Wavelet absolute mean_8\n", "363: accx_Wavelet energy_0\n", "364: accx_Wavelet energy_1\n", "365: accx_Wavelet energy_2\n", "366: accx_Wavelet energy_3\n", "367: accx_Wavelet energy_4\n", "368: accx_Wavelet energy_5\n", "369: accx_Wavelet energy_6\n", "370: accx_Wavelet energy_7\n", "371: accx_Wavelet energy_8\n", "372: accx_Wavelet entropy\n", "373: accx_Wavelet standard deviation_0\n", "374: accx_Wavelet standard deviation_1\n", "375: accx_Wavelet standard deviation_2\n", "376: accx_Wavelet standard deviation_3\n", "377: accx_Wavelet standard deviation_4\n", "378: accx_Wavelet standard deviation_5\n", "379: accx_Wavelet standard deviation_6\n", "380: accx_Wavelet standard deviation_7\n", "381: accx_Wavelet standard deviation_8\n", "382: accx_Wavelet variance_0\n", "383: accx_Wavelet variance_1\n", "384: accx_Wavelet variance_2\n", "385: accx_Wavelet variance_3\n", "386: accx_Wavelet variance_4\n", "387: accx_Wavelet variance_5\n", "388: accx_Wavelet variance_6\n", "389: accx_Wavelet variance_7\n", "390: accx_Wavelet variance_8\n", "391: accx_Zero crossing rate\n", "392: accy_Absolute energy\n", "393: accy_Area under the curve\n", "394: accy_Autocorrelation\n", "395: accy_Average power\n", "396: accy_Centroid\n", "397: accy_Detrended fluctuation analysis\n", "398: accy_ECDF Percentile Count_0\n", "399: accy_ECDF Percentile Count_1\n", "400: accy_ECDF Percentile_0\n", "401: accy_ECDF Percentile_1\n", "402: accy_ECDF_0\n", "403: accy_ECDF_1\n", "404: accy_ECDF_2\n", "405: accy_ECDF_3\n", "406: accy_ECDF_4\n", "407: accy_ECDF_5\n", "408: accy_ECDF_6\n", "409: accy_ECDF_7\n", "410: accy_ECDF_8\n", "411: accy_ECDF_9\n", "412: accy_Entropy\n", "413: accy_FFT mean coefficient_0\n", "414: accy_FFT mean coefficient_1\n", "415: accy_FFT mean coefficient_10\n", "416: accy_FFT mean coefficient_100\n", "417: accy_FFT mean coefficient_101\n", "418: accy_FFT mean coefficient_102\n", "419: accy_FFT mean coefficient_103\n", "420: accy_FFT mean coefficient_104\n", "421: accy_FFT mean coefficient_105\n", "422: accy_FFT mean coefficient_106\n", "423: accy_FFT mean coefficient_107\n", "424: accy_FFT mean coefficient_108\n", "425: accy_FFT mean coefficient_109\n", "426: accy_FFT mean coefficient_11\n", "427: accy_FFT mean coefficient_110\n", "428: accy_FFT mean coefficient_111\n", "429: accy_FFT mean coefficient_112\n", "430: accy_FFT mean coefficient_113\n", "431: accy_FFT mean coefficient_114\n", "432: accy_FFT mean coefficient_115\n", "433: accy_FFT mean coefficient_116\n", "434: accy_FFT mean coefficient_117\n", "435: accy_FFT mean coefficient_118\n", "436: accy_FFT mean coefficient_119\n", "437: accy_FFT mean coefficient_12\n", "438: accy_FFT mean coefficient_120\n", "439: accy_FFT mean coefficient_121\n", "440: accy_FFT mean coefficient_122\n", "441: accy_FFT mean coefficient_123\n", "442: accy_FFT mean coefficient_124\n", "443: accy_FFT mean coefficient_125\n", "444: accy_FFT mean coefficient_126\n", "445: accy_FFT mean coefficient_127\n", "446: accy_FFT mean coefficient_128\n", "447: accy_FFT mean coefficient_129\n", "448: accy_FFT mean coefficient_13\n", "449: accy_FFT mean coefficient_130\n", "450: accy_FFT mean coefficient_131\n", "451: accy_FFT mean coefficient_132\n", "452: accy_FFT mean coefficient_133\n", "453: accy_FFT mean coefficient_134\n", "454: accy_FFT mean coefficient_135\n", "455: accy_FFT mean coefficient_136\n", "456: accy_FFT mean coefficient_137\n", "457: accy_FFT mean coefficient_138\n", "458: accy_FFT mean coefficient_139\n", "459: accy_FFT mean coefficient_14\n", "460: accy_FFT mean coefficient_140\n", "461: accy_FFT mean coefficient_141\n", "462: accy_FFT mean coefficient_142\n", "463: accy_FFT mean coefficient_143\n", "464: accy_FFT mean coefficient_144\n", "465: accy_FFT mean coefficient_145\n", "466: accy_FFT mean coefficient_146\n", "467: accy_FFT mean coefficient_147\n", "468: accy_FFT mean coefficient_148\n", "469: accy_FFT mean coefficient_149\n", "470: accy_FFT mean coefficient_15\n", "471: accy_FFT mean coefficient_150\n", "472: accy_FFT mean coefficient_151\n", "473: accy_FFT mean coefficient_152\n", "474: accy_FFT mean coefficient_153\n", "475: accy_FFT mean coefficient_154\n", "476: accy_FFT mean coefficient_155\n", "477: accy_FFT mean coefficient_156\n", "478: accy_FFT mean coefficient_157\n", "479: accy_FFT mean coefficient_158\n", "480: accy_FFT mean coefficient_159\n", "481: accy_FFT mean coefficient_16\n", "482: accy_FFT mean coefficient_160\n", "483: accy_FFT mean coefficient_161\n", "484: accy_FFT mean coefficient_162\n", "485: accy_FFT mean coefficient_163\n", "486: accy_FFT mean coefficient_164\n", "487: accy_FFT mean coefficient_165\n", "488: accy_FFT mean coefficient_166\n", "489: accy_FFT mean coefficient_167\n", "490: accy_FFT mean coefficient_168\n", "491: accy_FFT mean coefficient_169\n", "492: accy_FFT mean coefficient_17\n", "493: accy_FFT mean coefficient_170\n", "494: accy_FFT mean coefficient_171\n", "495: accy_FFT mean coefficient_172\n", "496: accy_FFT mean coefficient_173\n", "497: accy_FFT mean coefficient_174\n", "498: accy_FFT mean coefficient_175\n", "499: accy_FFT mean coefficient_176\n", "500: accy_FFT mean coefficient_177\n", "501: accy_FFT mean coefficient_178\n", "502: accy_FFT mean coefficient_179\n", "503: accy_FFT mean coefficient_18\n", "504: accy_FFT mean coefficient_180\n", "505: accy_FFT mean coefficient_181\n", "506: accy_FFT mean coefficient_182\n", "507: accy_FFT mean coefficient_183\n", "508: accy_FFT mean coefficient_184\n", "509: accy_FFT mean coefficient_185\n", "510: accy_FFT mean coefficient_186\n", "511: accy_FFT mean coefficient_187\n", "512: accy_FFT mean coefficient_188\n", "513: accy_FFT mean coefficient_189\n", "514: accy_FFT mean coefficient_19\n", "515: accy_FFT mean coefficient_190\n", "516: accy_FFT mean coefficient_191\n", "517: accy_FFT mean coefficient_192\n", "518: accy_FFT mean coefficient_193\n", "519: accy_FFT mean coefficient_194\n", "520: accy_FFT mean coefficient_195\n", "521: accy_FFT mean coefficient_196\n", "522: accy_FFT mean coefficient_197\n", "523: accy_FFT mean coefficient_198\n", "524: accy_FFT mean coefficient_199\n", "525: accy_FFT mean coefficient_2\n", "526: accy_FFT mean coefficient_20\n", "527: accy_FFT mean coefficient_200\n", "528: accy_FFT mean coefficient_201\n", "529: accy_FFT mean coefficient_202\n", "530: accy_FFT mean coefficient_203\n", "531: accy_FFT mean coefficient_204\n", "532: accy_FFT mean coefficient_205\n", "533: accy_FFT mean coefficient_206\n", "534: accy_FFT mean coefficient_207\n", "535: accy_FFT mean coefficient_208\n", "536: accy_FFT mean coefficient_209\n", "537: accy_FFT mean coefficient_21\n", "538: accy_FFT mean coefficient_210\n", "539: accy_FFT mean coefficient_211\n", "540: accy_FFT mean coefficient_212\n", "541: accy_FFT mean coefficient_213\n", "542: accy_FFT mean coefficient_214\n", "543: accy_FFT mean coefficient_215\n", "544: accy_FFT mean coefficient_216\n", "545: accy_FFT mean coefficient_217\n", "546: accy_FFT mean coefficient_218\n", "547: accy_FFT mean coefficient_219\n", "548: accy_FFT mean coefficient_22\n", "549: accy_FFT mean coefficient_220\n", "550: accy_FFT mean coefficient_221\n", "551: accy_FFT mean coefficient_222\n", "552: accy_FFT mean coefficient_223\n", "553: accy_FFT mean coefficient_224\n", "554: accy_FFT mean coefficient_225\n", "555: accy_FFT mean coefficient_226\n", "556: accy_FFT mean coefficient_227\n", "557: accy_FFT mean coefficient_228\n", "558: accy_FFT mean coefficient_229\n", "559: accy_FFT mean coefficient_23\n", "560: accy_FFT mean coefficient_230\n", "561: accy_FFT mean coefficient_231\n", "562: accy_FFT mean coefficient_232\n", "563: accy_FFT mean coefficient_233\n", "564: accy_FFT mean coefficient_234\n", "565: accy_FFT mean coefficient_235\n", "566: accy_FFT mean coefficient_236\n", "567: accy_FFT mean coefficient_237\n", "568: accy_FFT mean coefficient_238\n", "569: accy_FFT mean coefficient_239\n", "570: accy_FFT mean coefficient_24\n", "571: accy_FFT mean coefficient_240\n", "572: accy_FFT mean coefficient_241\n", "573: accy_FFT mean coefficient_242\n", "574: accy_FFT mean coefficient_243\n", "575: accy_FFT mean coefficient_244\n", "576: accy_FFT mean coefficient_245\n", "577: accy_FFT mean coefficient_246\n", "578: accy_FFT mean coefficient_247\n", "579: accy_FFT mean coefficient_248\n", "580: accy_FFT mean coefficient_249\n", "581: accy_FFT mean coefficient_25\n", "582: accy_FFT mean coefficient_250\n", "583: accy_FFT mean coefficient_26\n", "584: accy_FFT mean coefficient_27\n", "585: accy_FFT mean coefficient_28\n", "586: accy_FFT mean coefficient_29\n", "587: accy_FFT mean coefficient_3\n", "588: accy_FFT mean coefficient_30\n", "589: accy_FFT mean coefficient_31\n", "590: accy_FFT mean coefficient_32\n", "591: accy_FFT mean coefficient_33\n", "592: accy_FFT mean coefficient_34\n", "593: accy_FFT mean coefficient_35\n", "594: accy_FFT mean coefficient_36\n", "595: accy_FFT mean coefficient_37\n", "596: accy_FFT mean coefficient_38\n", "597: accy_FFT mean coefficient_39\n", "598: accy_FFT mean coefficient_4\n", "599: accy_FFT mean coefficient_40\n", "600: accy_FFT mean coefficient_41\n", "601: accy_FFT mean coefficient_42\n", "602: accy_FFT mean coefficient_43\n", "603: accy_FFT mean coefficient_44\n", "604: accy_FFT mean coefficient_45\n", "605: accy_FFT mean coefficient_46\n", "606: accy_FFT mean coefficient_47\n", "607: accy_FFT mean coefficient_48\n", "608: accy_FFT mean coefficient_49\n", "609: accy_FFT mean coefficient_5\n", "610: accy_FFT mean coefficient_50\n", "611: accy_FFT mean coefficient_51\n", "612: accy_FFT mean coefficient_52\n", "613: accy_FFT mean coefficient_53\n", "614: accy_FFT mean coefficient_54\n", "615: accy_FFT mean coefficient_55\n", "616: accy_FFT mean coefficient_56\n", "617: accy_FFT mean coefficient_57\n", "618: accy_FFT mean coefficient_58\n", "619: accy_FFT mean coefficient_59\n", "620: accy_FFT mean coefficient_6\n", "621: accy_FFT mean coefficient_60\n", "622: accy_FFT mean coefficient_61\n", "623: accy_FFT mean coefficient_62\n", "624: accy_FFT mean coefficient_63\n", "625: accy_FFT mean coefficient_64\n", "626: accy_FFT mean coefficient_65\n", "627: accy_FFT mean coefficient_66\n", "628: accy_FFT mean coefficient_67\n", "629: accy_FFT mean coefficient_68\n", "630: accy_FFT mean coefficient_69\n", "631: accy_FFT mean coefficient_7\n", "632: accy_FFT mean coefficient_70\n", "633: accy_FFT mean coefficient_71\n", "634: accy_FFT mean coefficient_72\n", "635: accy_FFT mean coefficient_73\n", "636: accy_FFT mean coefficient_74\n", "637: accy_FFT mean coefficient_75\n", "638: accy_FFT mean coefficient_76\n", "639: accy_FFT mean coefficient_77\n", "640: accy_FFT mean coefficient_78\n", "641: accy_FFT mean coefficient_79\n", "642: accy_FFT mean coefficient_8\n", "643: accy_FFT mean coefficient_80\n", "644: accy_FFT mean coefficient_81\n", "645: accy_FFT mean coefficient_82\n", "646: accy_FFT mean coefficient_83\n", "647: accy_FFT mean coefficient_84\n", "648: accy_FFT mean coefficient_85\n", "649: accy_FFT mean coefficient_86\n", "650: accy_FFT mean coefficient_87\n", "651: accy_FFT mean coefficient_88\n", "652: accy_FFT mean coefficient_89\n", "653: accy_FFT mean coefficient_9\n", "654: accy_FFT mean coefficient_90\n", "655: accy_FFT mean coefficient_91\n", "656: accy_FFT mean coefficient_92\n", "657: accy_FFT mean coefficient_93\n", "658: accy_FFT mean coefficient_94\n", "659: accy_FFT mean coefficient_95\n", "660: accy_FFT mean coefficient_96\n", "661: accy_FFT mean coefficient_97\n", "662: accy_FFT mean coefficient_98\n", "663: accy_FFT mean coefficient_99\n", "664: accy_Fundamental frequency\n", "665: acc<PERSON>_<PERSON> fractal dimension\n", "666: accy_Histogram_0\n", "667: accy_Histogram_1\n", "668: accy_Histogram_2\n", "669: accy_Histogram_3\n", "670: accy_Histogram_4\n", "671: accy_Histogram_5\n", "672: accy_Histogram_6\n", "673: accy_Histogram_7\n", "674: accy_Histogram_8\n", "675: accy_Histogram_9\n", "676: accy_Human range energy\n", "677: acc<PERSON>_<PERSON><PERSON> exponent\n", "678: accy_Interquartile range\n", "679: a<PERSON><PERSON>_<PERSON>\n", "680: accy_LPCC_0\n", "681: accy_LPCC_1\n", "682: accy_LPCC_10\n", "683: accy_LPCC_11\n", "684: accy_LPCC_2\n", "685: accy_LPCC_3\n", "686: accy_LPCC_4\n", "687: accy_LPCC_5\n", "688: accy_LPCC_6\n", "689: accy_LPCC_7\n", "690: accy_LPCC_8\n", "691: accy_LPCC_9\n", "692: accy_Lempel-Ziv complexity\n", "693: accy_MFCC_0\n", "694: accy_MFCC_1\n", "695: accy_MFCC_10\n", "696: accy_MFCC_11\n", "697: accy_MFCC_2\n", "698: accy_MFCC_3\n", "699: accy_MFCC_4\n", "700: accy_MFCC_5\n", "701: accy_MFCC_6\n", "702: accy_MFCC_7\n", "703: accy_MFCC_8\n", "704: accy_MFCC_9\n", "705: accy_<PERSON>\n", "706: accy_Max power spectrum\n", "707: accy_Maximum fractal length\n", "708: accy_Maximum frequency\n", "709: accy_<PERSON>\n", "710: accy_Mean absolute deviation\n", "711: accy_Mean absolute diff\n", "712: accy_Mean diff\n", "713: accy_Median\n", "714: accy_Median absolute deviation\n", "715: accy_Median absolute diff\n", "716: accy_Median diff\n", "717: accy_Median frequency\n", "718: acc<PERSON>_<PERSON>\n", "719: accy_Multiscale entropy\n", "720: accy_Negative turning points\n", "721: accy_Neighbourhood peaks\n", "722: accy_Peak to peak distance\n", "723: accy_Petrosian fractal dimension\n", "724: accy_Positive turning points\n", "725: accy_Power bandwidth\n", "726: accy_Root mean square\n", "727: accy_Signal distance\n", "728: accy_Skewness\n", "729: acc<PERSON>_<PERSON>lope\n", "730: accy_Spectral centroid\n", "731: accy_Spectral decrease\n", "732: accy_Spectral distance\n", "733: accy_Spectral entropy\n", "734: accy_Spectral kurtosis\n", "735: accy_Spectral positive turning points\n", "736: accy_Spectral roll-off\n", "737: accy_Spectral roll-on\n", "738: accy_Spectral skewness\n", "739: accy_Spectral slope\n", "740: accy_Spectral spread\n", "741: accy_Spectral variation\n", "742: accy_Standard deviation\n", "743: accy_Sum absolute diff\n", "744: acc<PERSON>_<PERSON><PERSON><PERSON>\n", "745: accy_Wavelet absolute mean_0\n", "746: accy_Wavelet absolute mean_1\n", "747: accy_Wavelet absolute mean_2\n", "748: accy_Wavelet absolute mean_3\n", "749: accy_Wavelet absolute mean_4\n", "750: accy_Wavelet absolute mean_5\n", "751: accy_Wavelet absolute mean_6\n", "752: accy_Wavelet absolute mean_7\n", "753: accy_Wavelet absolute mean_8\n", "754: accy_Wavelet energy_0\n", "755: accy_Wavelet energy_1\n", "756: accy_Wavelet energy_2\n", "757: accy_Wavelet energy_3\n", "758: accy_Wavelet energy_4\n", "759: accy_Wavelet energy_5\n", "760: accy_Wavelet energy_6\n", "761: accy_Wavelet energy_7\n", "762: accy_Wavelet energy_8\n", "763: accy_Wavelet entropy\n", "764: accy_Wavelet standard deviation_0\n", "765: accy_Wavelet standard deviation_1\n", "766: accy_Wavelet standard deviation_2\n", "767: accy_Wavelet standard deviation_3\n", "768: accy_Wavelet standard deviation_4\n", "769: accy_Wavelet standard deviation_5\n", "770: accy_Wavelet standard deviation_6\n", "771: accy_Wavelet standard deviation_7\n", "772: accy_Wavelet standard deviation_8\n", "773: accy_Wavelet variance_0\n", "774: accy_Wavelet variance_1\n", "775: accy_Wavelet variance_2\n", "776: accy_Wavelet variance_3\n", "777: accy_Wavelet variance_4\n", "778: accy_Wavelet variance_5\n", "779: accy_Wavelet variance_6\n", "780: accy_Wavelet variance_7\n", "781: accy_Wavelet variance_8\n", "782: accy_Zero crossing rate\n", "783: accz_Absolute energy\n", "784: accz_Area under the curve\n", "785: accz_Autocorrelation\n", "786: accz_Average power\n", "787: accz_Centroid\n", "788: accz_Detrended fluctuation analysis\n", "789: accz_ECDF Percentile Count_0\n", "790: accz_ECDF Percentile Count_1\n", "791: accz_ECDF Percentile_0\n", "792: accz_ECDF Percentile_1\n", "793: accz_ECDF_0\n", "794: accz_ECDF_1\n", "795: accz_ECDF_2\n", "796: accz_ECDF_3\n", "797: accz_ECDF_4\n", "798: accz_ECDF_5\n", "799: accz_ECDF_6\n", "800: accz_ECDF_7\n", "801: accz_ECDF_8\n", "802: accz_ECDF_9\n", "803: accz_Entropy\n", "804: accz_FFT mean coefficient_0\n", "805: accz_FFT mean coefficient_1\n", "806: accz_FFT mean coefficient_10\n", "807: accz_FFT mean coefficient_100\n", "808: accz_FFT mean coefficient_101\n", "809: accz_FFT mean coefficient_102\n", "810: accz_FFT mean coefficient_103\n", "811: accz_FFT mean coefficient_104\n", "812: accz_FFT mean coefficient_105\n", "813: accz_FFT mean coefficient_106\n", "814: accz_FFT mean coefficient_107\n", "815: accz_FFT mean coefficient_108\n", "816: accz_FFT mean coefficient_109\n", "817: accz_FFT mean coefficient_11\n", "818: accz_FFT mean coefficient_110\n", "819: accz_FFT mean coefficient_111\n", "820: accz_FFT mean coefficient_112\n", "821: accz_FFT mean coefficient_113\n", "822: accz_FFT mean coefficient_114\n", "823: accz_FFT mean coefficient_115\n", "824: accz_FFT mean coefficient_116\n", "825: accz_FFT mean coefficient_117\n", "826: accz_FFT mean coefficient_118\n", "827: accz_FFT mean coefficient_119\n", "828: accz_FFT mean coefficient_12\n", "829: accz_FFT mean coefficient_120\n", "830: accz_FFT mean coefficient_121\n", "831: accz_FFT mean coefficient_122\n", "832: accz_FFT mean coefficient_123\n", "833: accz_FFT mean coefficient_124\n", "834: accz_FFT mean coefficient_125\n", "835: accz_FFT mean coefficient_126\n", "836: accz_FFT mean coefficient_127\n", "837: accz_FFT mean coefficient_128\n", "838: accz_FFT mean coefficient_129\n", "839: accz_FFT mean coefficient_13\n", "840: accz_FFT mean coefficient_130\n", "841: accz_FFT mean coefficient_131\n", "842: accz_FFT mean coefficient_132\n", "843: accz_FFT mean coefficient_133\n", "844: accz_FFT mean coefficient_134\n", "845: accz_FFT mean coefficient_135\n", "846: accz_FFT mean coefficient_136\n", "847: accz_FFT mean coefficient_137\n", "848: accz_FFT mean coefficient_138\n", "849: accz_FFT mean coefficient_139\n", "850: accz_FFT mean coefficient_14\n", "851: accz_FFT mean coefficient_140\n", "852: accz_FFT mean coefficient_141\n", "853: accz_FFT mean coefficient_142\n", "854: accz_FFT mean coefficient_143\n", "855: accz_FFT mean coefficient_144\n", "856: accz_FFT mean coefficient_145\n", "857: accz_FFT mean coefficient_146\n", "858: accz_FFT mean coefficient_147\n", "859: accz_FFT mean coefficient_148\n", "860: accz_FFT mean coefficient_149\n", "861: accz_FFT mean coefficient_15\n", "862: accz_FFT mean coefficient_150\n", "863: accz_FFT mean coefficient_151\n", "864: accz_FFT mean coefficient_152\n", "865: accz_FFT mean coefficient_153\n", "866: accz_FFT mean coefficient_154\n", "867: accz_FFT mean coefficient_155\n", "868: accz_FFT mean coefficient_156\n", "869: accz_FFT mean coefficient_157\n", "870: accz_FFT mean coefficient_158\n", "871: accz_FFT mean coefficient_159\n", "872: accz_FFT mean coefficient_16\n", "873: accz_FFT mean coefficient_160\n", "874: accz_FFT mean coefficient_161\n", "875: accz_FFT mean coefficient_162\n", "876: accz_FFT mean coefficient_163\n", "877: accz_FFT mean coefficient_164\n", "878: accz_FFT mean coefficient_165\n", "879: accz_FFT mean coefficient_166\n", "880: accz_FFT mean coefficient_167\n", "881: accz_FFT mean coefficient_168\n", "882: accz_FFT mean coefficient_169\n", "883: accz_FFT mean coefficient_17\n", "884: accz_FFT mean coefficient_170\n", "885: accz_FFT mean coefficient_171\n", "886: accz_FFT mean coefficient_172\n", "887: accz_FFT mean coefficient_173\n", "888: accz_FFT mean coefficient_174\n", "889: accz_FFT mean coefficient_175\n", "890: accz_FFT mean coefficient_176\n", "891: accz_FFT mean coefficient_177\n", "892: accz_FFT mean coefficient_178\n", "893: accz_FFT mean coefficient_179\n", "894: accz_FFT mean coefficient_18\n", "895: accz_FFT mean coefficient_180\n", "896: accz_FFT mean coefficient_181\n", "897: accz_FFT mean coefficient_182\n", "898: accz_FFT mean coefficient_183\n", "899: accz_FFT mean coefficient_184\n", "900: accz_FFT mean coefficient_185\n", "901: accz_FFT mean coefficient_186\n", "902: accz_FFT mean coefficient_187\n", "903: accz_FFT mean coefficient_188\n", "904: accz_FFT mean coefficient_189\n", "905: accz_FFT mean coefficient_19\n", "906: accz_FFT mean coefficient_190\n", "907: accz_FFT mean coefficient_191\n", "908: accz_FFT mean coefficient_192\n", "909: accz_FFT mean coefficient_193\n", "910: accz_FFT mean coefficient_194\n", "911: accz_FFT mean coefficient_195\n", "912: accz_FFT mean coefficient_196\n", "913: accz_FFT mean coefficient_197\n", "914: accz_FFT mean coefficient_198\n", "915: accz_FFT mean coefficient_199\n", "916: accz_FFT mean coefficient_2\n", "917: accz_FFT mean coefficient_20\n", "918: accz_FFT mean coefficient_200\n", "919: accz_FFT mean coefficient_201\n", "920: accz_FFT mean coefficient_202\n", "921: accz_FFT mean coefficient_203\n", "922: accz_FFT mean coefficient_204\n", "923: accz_FFT mean coefficient_205\n", "924: accz_FFT mean coefficient_206\n", "925: accz_FFT mean coefficient_207\n", "926: accz_FFT mean coefficient_208\n", "927: accz_FFT mean coefficient_209\n", "928: accz_FFT mean coefficient_21\n", "929: accz_FFT mean coefficient_210\n", "930: accz_FFT mean coefficient_211\n", "931: accz_FFT mean coefficient_212\n", "932: accz_FFT mean coefficient_213\n", "933: accz_FFT mean coefficient_214\n", "934: accz_FFT mean coefficient_215\n", "935: accz_FFT mean coefficient_216\n", "936: accz_FFT mean coefficient_217\n", "937: accz_FFT mean coefficient_218\n", "938: accz_FFT mean coefficient_219\n", "939: accz_FFT mean coefficient_22\n", "940: accz_FFT mean coefficient_220\n", "941: accz_FFT mean coefficient_221\n", "942: accz_FFT mean coefficient_222\n", "943: accz_FFT mean coefficient_223\n", "944: accz_FFT mean coefficient_224\n", "945: accz_FFT mean coefficient_225\n", "946: accz_FFT mean coefficient_226\n", "947: accz_FFT mean coefficient_227\n", "948: accz_FFT mean coefficient_228\n", "949: accz_FFT mean coefficient_229\n", "950: accz_FFT mean coefficient_23\n", "951: accz_FFT mean coefficient_230\n", "952: accz_FFT mean coefficient_231\n", "953: accz_FFT mean coefficient_232\n", "954: accz_FFT mean coefficient_233\n", "955: accz_FFT mean coefficient_234\n", "956: accz_FFT mean coefficient_235\n", "957: accz_FFT mean coefficient_236\n", "958: accz_FFT mean coefficient_237\n", "959: accz_FFT mean coefficient_238\n", "960: accz_FFT mean coefficient_239\n", "961: accz_FFT mean coefficient_24\n", "962: accz_FFT mean coefficient_240\n", "963: accz_FFT mean coefficient_241\n", "964: accz_FFT mean coefficient_242\n", "965: accz_FFT mean coefficient_243\n", "966: accz_FFT mean coefficient_244\n", "967: accz_FFT mean coefficient_245\n", "968: accz_FFT mean coefficient_246\n", "969: accz_FFT mean coefficient_247\n", "970: accz_FFT mean coefficient_248\n", "971: accz_FFT mean coefficient_249\n", "972: accz_FFT mean coefficient_25\n", "973: accz_FFT mean coefficient_250\n", "974: accz_FFT mean coefficient_26\n", "975: accz_FFT mean coefficient_27\n", "976: accz_FFT mean coefficient_28\n", "977: accz_FFT mean coefficient_29\n", "978: accz_FFT mean coefficient_3\n", "979: accz_FFT mean coefficient_30\n", "980: accz_FFT mean coefficient_31\n", "981: accz_FFT mean coefficient_32\n", "982: accz_FFT mean coefficient_33\n", "983: accz_FFT mean coefficient_34\n", "984: accz_FFT mean coefficient_35\n", "985: accz_FFT mean coefficient_36\n", "986: accz_FFT mean coefficient_37\n", "987: accz_FFT mean coefficient_38\n", "988: accz_FFT mean coefficient_39\n", "989: accz_FFT mean coefficient_4\n", "990: accz_FFT mean coefficient_40\n", "991: accz_FFT mean coefficient_41\n", "992: accz_FFT mean coefficient_42\n", "993: accz_FFT mean coefficient_43\n", "994: accz_FFT mean coefficient_44\n", "995: accz_FFT mean coefficient_45\n", "996: accz_FFT mean coefficient_46\n", "997: accz_FFT mean coefficient_47\n", "998: accz_FFT mean coefficient_48\n", "999: accz_FFT mean coefficient_49\n", "1000: accz_FFT mean coefficient_5\n", "1001: accz_FFT mean coefficient_50\n", "1002: accz_FFT mean coefficient_51\n", "1003: accz_FFT mean coefficient_52\n", "1004: accz_FFT mean coefficient_53\n", "1005: accz_FFT mean coefficient_54\n", "1006: accz_FFT mean coefficient_55\n", "1007: accz_FFT mean coefficient_56\n", "1008: accz_FFT mean coefficient_57\n", "1009: accz_FFT mean coefficient_58\n", "1010: accz_FFT mean coefficient_59\n", "1011: accz_FFT mean coefficient_6\n", "1012: accz_FFT mean coefficient_60\n", "1013: accz_FFT mean coefficient_61\n", "1014: accz_FFT mean coefficient_62\n", "1015: accz_FFT mean coefficient_63\n", "1016: accz_FFT mean coefficient_64\n", "1017: accz_FFT mean coefficient_65\n", "1018: accz_FFT mean coefficient_66\n", "1019: accz_FFT mean coefficient_67\n", "1020: accz_FFT mean coefficient_68\n", "1021: accz_FFT mean coefficient_69\n", "1022: accz_FFT mean coefficient_7\n", "1023: accz_FFT mean coefficient_70\n", "1024: accz_FFT mean coefficient_71\n", "1025: accz_FFT mean coefficient_72\n", "1026: accz_FFT mean coefficient_73\n", "1027: accz_FFT mean coefficient_74\n", "1028: accz_FFT mean coefficient_75\n", "1029: accz_FFT mean coefficient_76\n", "1030: accz_FFT mean coefficient_77\n", "1031: accz_FFT mean coefficient_78\n", "1032: accz_FFT mean coefficient_79\n", "1033: accz_FFT mean coefficient_8\n", "1034: accz_FFT mean coefficient_80\n", "1035: accz_FFT mean coefficient_81\n", "1036: accz_FFT mean coefficient_82\n", "1037: accz_FFT mean coefficient_83\n", "1038: accz_FFT mean coefficient_84\n", "1039: accz_FFT mean coefficient_85\n", "1040: accz_FFT mean coefficient_86\n", "1041: accz_FFT mean coefficient_87\n", "1042: accz_FFT mean coefficient_88\n", "1043: accz_FFT mean coefficient_89\n", "1044: accz_FFT mean coefficient_9\n", "1045: accz_FFT mean coefficient_90\n", "1046: accz_FFT mean coefficient_91\n", "1047: accz_FFT mean coefficient_92\n", "1048: accz_FFT mean coefficient_93\n", "1049: accz_FFT mean coefficient_94\n", "1050: accz_FFT mean coefficient_95\n", "1051: accz_FFT mean coefficient_96\n", "1052: accz_FFT mean coefficient_97\n", "1053: accz_FFT mean coefficient_98\n", "1054: accz_FFT mean coefficient_99\n", "1055: accz_Fundamental frequency\n", "1056: accz_<PERSON> fractal dimension\n", "1057: accz_Histogram_0\n", "1058: accz_Histogram_1\n", "1059: accz_Histogram_2\n", "1060: accz_Histogram_3\n", "1061: accz_Histogram_4\n", "1062: accz_Histogram_5\n", "1063: accz_Histogram_6\n", "1064: accz_Histogram_7\n", "1065: accz_Histogram_8\n", "1066: accz_Histogram_9\n", "1067: accz_Human range energy\n", "1068: accz_Hurst exponent\n", "1069: accz_Interquartile range\n", "1070: acc<PERSON>_<PERSON><PERSON>\n", "1071: accz_LPCC_0\n", "1072: accz_LPCC_1\n", "1073: accz_LPCC_10\n", "1074: accz_LPCC_11\n", "1075: accz_LPCC_2\n", "1076: accz_LPCC_3\n", "1077: accz_LPCC_4\n", "1078: accz_LPCC_5\n", "1079: accz_LPCC_6\n", "1080: accz_LPCC_7\n", "1081: accz_LPCC_8\n", "1082: accz_LPCC_9\n", "1083: accz_Lempel-Ziv complexity\n", "1084: accz_MFCC_0\n", "1085: accz_MFCC_1\n", "1086: accz_MFCC_10\n", "1087: accz_MFCC_11\n", "1088: accz_MFCC_2\n", "1089: accz_MFCC_3\n", "1090: accz_MFCC_4\n", "1091: accz_MFCC_5\n", "1092: accz_MFCC_6\n", "1093: accz_MFCC_7\n", "1094: accz_MFCC_8\n", "1095: accz_MFCC_9\n", "1096: accz_Max\n", "1097: accz_Max power spectrum\n", "1098: accz_Maximum fractal length\n", "1099: accz_Maximum frequency\n", "1100: accz_Mean\n", "1101: accz_Mean absolute deviation\n", "1102: accz_Mean absolute diff\n", "1103: accz_Mean diff\n", "1104: accz_Median\n", "1105: accz_Median absolute deviation\n", "1106: accz_Median absolute diff\n", "1107: accz_Median diff\n", "1108: accz_Median frequency\n", "1109: accz_Min\n", "1110: accz_Multiscale entropy\n", "1111: accz_Negative turning points\n", "1112: accz_Neighbourhood peaks\n", "1113: accz_Peak to peak distance\n", "1114: accz_Petrosian fractal dimension\n", "1115: accz_Positive turning points\n", "1116: accz_Power bandwidth\n", "1117: accz_Root mean square\n", "1118: accz_Signal distance\n", "1119: accz_Skewness\n", "1120: accz_Slope\n", "1121: accz_Spectral centroid\n", "1122: accz_Spectral decrease\n", "1123: accz_Spectral distance\n", "1124: accz_Spectral entropy\n", "1125: accz_Spectral kurtosis\n", "1126: accz_Spectral positive turning points\n", "1127: accz_Spectral roll-off\n", "1128: accz_Spectral roll-on\n", "1129: accz_Spectral skewness\n", "1130: accz_Spectral slope\n", "1131: accz_Spectral spread\n", "1132: accz_Spectral variation\n", "1133: accz_Standard deviation\n", "1134: accz_Sum absolute diff\n", "1135: accz_<PERSON><PERSON><PERSON>\n", "1136: accz_Wavelet absolute mean_0\n", "1137: accz_Wavelet absolute mean_1\n", "1138: accz_Wavelet absolute mean_2\n", "1139: accz_Wavelet absolute mean_3\n", "1140: accz_Wavelet absolute mean_4\n", "1141: accz_Wavelet absolute mean_5\n", "1142: accz_Wavelet absolute mean_6\n", "1143: accz_Wavelet absolute mean_7\n", "1144: accz_Wavelet absolute mean_8\n", "1145: accz_Wavelet energy_0\n", "1146: accz_Wavelet energy_1\n", "1147: accz_Wavelet energy_2\n", "1148: accz_Wavelet energy_3\n", "1149: accz_Wavelet energy_4\n", "1150: accz_Wavelet energy_5\n", "1151: accz_Wavelet energy_6\n", "1152: accz_Wavelet energy_7\n", "1153: accz_Wavelet energy_8\n", "1154: accz_Wavelet entropy\n", "1155: accz_Wavelet standard deviation_0\n", "1156: accz_Wavelet standard deviation_1\n", "1157: accz_Wavelet standard deviation_2\n", "1158: accz_Wavelet standard deviation_3\n", "1159: accz_Wavelet standard deviation_4\n", "1160: accz_Wavelet standard deviation_5\n", "1161: accz_Wavelet standard deviation_6\n", "1162: accz_Wavelet standard deviation_7\n", "1163: accz_Wavelet standard deviation_8\n", "1164: accz_Wavelet variance_0\n", "1165: accz_Wavelet variance_1\n", "1166: accz_Wavelet variance_2\n", "1167: accz_Wavelet variance_3\n", "1168: accz_Wavelet variance_4\n", "1169: accz_Wavelet variance_5\n", "1170: accz_Wavelet variance_6\n", "1171: accz_Wavelet variance_7\n", "1172: accz_Wavelet variance_8\n", "1173: accz_Zero crossing rate\n", "Filtered features saved in the directory: TSFEL_filtereddataset\n"]}], "source": ["selected_feature_indices = np.array([\n", "    0, 1, 3, 6, 7, 260, 298, 300, 302, 303, 304, 306, 308, 311,\n", "    315, 319, 325, 335, 336, 339, 340, 341, 342, 343, 344, 345,\n", "    346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 357, 358,\n", "    359, 360, 361, 362, 363, 364, 365, 374, 679, 683, 687, 689,\n", "    690, 715, 726, 727, 728, 736, 737, 738, 744, 1058\n", "])\n", "\n", "print(selected_feature_indices)\n", "\n", "original_dir = 'TSFEL_dataset'\n", "filtered_dir = 'TSFEL_filtereddataset'\n", "shutil.copytree(original_dir, filtered_dir)\n", "\n", "feature_names = None\n", "\n", "for root, dirs, files in os.walk(filtered_dir):\n", "    for file in files:\n", "        if file.endswith('.csv'):\n", "            file_path = os.path.join(root, file)\n", "            \n", "            df = pd.read_csv(file_path)\n", "            \n", "            if feature_names is None:\n", "                feature_names = df.columns.tolist()\n", "            \n", "            df_filtered = df.iloc[:, selected_feature_indices]\n", "            \n", "            df_filtered.to_csv(file_path, index=False)\n", "\n", "if feature_names is not None:\n", "    print(\"List of features in the dataset:\")\n", "    for i, feature in enumerate(feature_names):\n", "        print(f\"{i+1}: {feature}\")\n", "\n", "print(f\"Filtered features saved in the directory: {filtered_dir}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}